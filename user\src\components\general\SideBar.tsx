"use client"

import * as React from "react"
import {
  LayoutGrid,
  MessageSquareText,
  ActivitySquare,
  BrainCircuit,
  ClipboardCheck,
  BookOpen,
  History,
  Settings,
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  Sidebar<PERSON>ooter,
  SidebarHeader,
  SidebarRail,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarTrigger,
  useSidebar
} from "@/components/ui/sidebar"

import Image from "next/image"

// Petals AI sidebar data matching the mock
const data = {
  user: {
    name: "Petals User",
    email: "<EMAIL>",
    avatar: "/images/avatar-placeholder.png",
  },
  teams: {
    name: "Petals AI",
    logo: "/images/petals_logo.svg",
    plan: "Pro",
  },
  nav: [
    {
      title: "Dashboard",
      url: "/",
      icon: LayoutGrid,
    },
    {
      title: "Chat with Petals",
      url: "/chat",
      icon: MessageSquareText,
    },
    {
      title: "Symptom Contextualizer",
      url: "/symptoms",
      icon: ActivitySquare,
    },
    {
      title: "Pain & Fatigue Companion",
      url: "/companion",
      icon: BrainCircuit,
    },
    {
      title: "Medication Optimizer",
      url: "/medications",
      icon: ClipboardCheck,
    },
    {
      title: "Menstrual Journal",
      url: "/menstrual",
      icon: BookOpen,
    },
    {
      title: "History",
      url: "/history",
      icon: History,
    },
  ],
}

// Petals Logo Component
function PetalsLogo({ className }: { className?: string }) {
  return (
    <Image
      src="/images/petals_logo.svg"
      alt="Petals AI Logo"
      width={32}
      height={32}
      className={className}
    />
  )
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  // Get current path to determine active menu item
  const [currentPath, setCurrentPath] = React.useState("/")
  const [isHovered, setIsHovered] = React.useState(false)
  const { state } = useSidebar()

  React.useEffect(() => {
    setCurrentPath(window.location.pathname)
  }, [])

  const isCollapsed = state === "collapsed"

  return (
    <Sidebar
      collapsible="icon"
      {...props}
      className="bg-sidebar"
      style={{ "--sidebar-width-icon": "4rem" } as React.CSSProperties}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <SidebarHeader className="px-2 py-6 group-data-[collapsible=icon]:px-2">
        <SidebarMenu>
          <SidebarMenuItem>
            {/* When sidebar is collapsed, show logo by default, trigger on hover */}
            <div className="flex items-center justify-between w-full data-[state=expanded]:flex data-[state=collapsed]:hidden">
              {!isCollapsed || !isHovered ? (
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <div className="text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                    <PetalsLogo className="size-6" />
                  </div>
                  {!isCollapsed &&
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-medium">{data.teams.name}</span>
                      <span className="truncate text-xs">{data.teams.plan}</span>
                    </div>}
                </SidebarMenuButton>
              ) : (
                <SidebarTrigger />
              )}
              {!isCollapsed && <SidebarTrigger className="ml-2" />}
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent className="px-2 py-2 group-data-[collapsible=icon]:px-2">
        <SidebarMenu className="space-y-1">
          {data.nav.map((item, index) => {
            const isActive = index === 0; // Example logic
            const buttonClassName = isActive
              ? 'h-12 px-3 rounded-lg transition-colors bg-green-600 text-white hover:bg-green-700 group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:px-0'
              : 'h-12 px-3 rounded-lg transition-colors text-gray-700 hover:bg-white/50 group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:px-0';

            return (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton
                  asChild
                  tooltip={item.title}
                  className={buttonClassName}
                >
                  <a href={item.url} className="flex items-center gap-3">
                    <item.icon className="size-5" />
                    <span className="font-medium">{item.title}</span>
                  </a>
                </SidebarMenuButton>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarContent>

      <SidebarFooter className="px-2 py-4 group-data-[collapsible=icon]:px-2">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              tooltip="Profile & Settings"
              className={`
                h-12 px-3 rounded-lg transition-colors
                ${currentPath === '/settings'
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'text-gray-700 hover:bg-white/50'
                }
              `}
            >
              <a href="/settings" className="flex items-center gap-3">
                <Settings className="size-5" />
                <span className="font-medium data-[collapsed=true]:hidden">Profile & Settings</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  )
}